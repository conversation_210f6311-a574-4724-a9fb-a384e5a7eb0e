/* Vblkena Hearing Design System */
:root{
	--c-bg:#f7fafc;
	--c-surface:#ffffff;
	--c-border:#e5e7eb;
	--c-brand:#0ea5a3;
	--c-brand-700:#0b8483;
	--c-accent:#4f9ed6;
	--c-text:#0f172a;
	--c-muted:#475569;
	--radius-lg:16px;
	--radius-md:12px;
	--radius-sm:8px;
	--shadow-sm:0 1px 2px rgba(0,0,0,.06);
	--shadow-md:0 6px 20px rgba(15,23,42,.08);
}

html{scroll-behavior:smooth}
body{background:var(--c-bg);color:var(--c-text);line-height:1.6}

.container{width:100%;max-width:1200px;margin:0 auto;padding:0 20px}

/* Typography */
h1,h2,h3,h4{font-family:Inter,IBM Plex Sans,system-ui,-apple-system,Se<PERSON>e <PERSON>,Roboto,Helvetica,Arial;letter-spacing:.2px;color:var(--c-text)}
h1{font-weight:700;font-size:clamp(28px,4vw,48px);line-height:1.1;margin:.2em 0}
h2{font-weight:700;font-size:clamp(22px,3vw,32px);line-height:1.2;margin:.6em 0 .4em}
p,li{color:var(--c-muted)}

/* Components */
.btn{display:inline-flex;align-items:center;justify-content:center;gap:8px;font-weight:600;text-decoration:none;border-radius:var(--radius-md);padding:12px 18px;border:1px solid transparent;transition:all .2s ease}
.btn-primary{background:var(--c-brand);color:#fff}
.btn-primary:hover{background:var(--c-brand-700)}
.btn-outline{background:transparent;border-color:var(--c-border);color:var(--c-text)}
.btn-outline:hover{border-color:var(--c-text)}

.card{background:var(--c-surface);border:1px solid var(--c-border);border-radius:var(--radius-lg);box-shadow:var(--shadow-sm)}
.card.elevated{box-shadow:var(--shadow-md)}

.grid{display:grid;gap:20px}
.grid.cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}
.grid.cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}
@media (max-width: 960px){.grid.cols-3,.grid.cols-2{grid-template-columns:1fr}}

/* Header adjustments for menu items when using items_wrap %3$s */
.nav-primary > li{list-style:none}
.nav-primary > li > a{padding:8px 0;display:inline-flex}

/* Hero */
.hero{background:linear-gradient(180deg,#ffffff, #f2fbfb);border-bottom:1px solid var(--c-border)}
.hero-inner{display:grid;grid-template-columns:1.2fr .8fr;align-items:center;gap:24px;padding:48px 0}
.hero h1{margin:0}
.hero .lede{font-size:18px}
.hero .media{display:flex;align-items:center;justify-content:center}
.hero .mock-device{width:100%;max-width:420px;aspect-ratio:3/4;border-radius:24px;background:linear-gradient(135deg,#e9f7ff,#e9fffb);border:1px solid var(--c-border);box-shadow:var(--shadow-md)}
@media (max-width: 960px){.hero-inner{grid-template-columns:1fr;padding:32px 0}}

/* Product highlight */
.product-highlight .item{padding:20px;border:1px solid var(--c-border);border-radius:var(--radius-lg);display:flex;flex-direction:column;gap:12px}
.product-highlight .price{font-weight:700;color:var(--c-text)}
.product-highlight .thumb{width:100%;aspect-ratio:1/1;border-radius:12px;background:#f0f9ff;border:1px solid var(--c-border)}

/* Footer */
.site-footer a{color:var(--c-muted);text-decoration:none}
.site-footer a:hover{color:var(--c-text)}

/* WooCommerce basic alignment */
.woocommerce .products .product{background:#fff;border:1px solid var(--c-border);border-radius:12px;padding:16px}
.woocommerce div.product div.images img{border-radius:12px}
.woocommerce div.product .summary{background:#fff;border:1px solid var(--c-border);border-radius:12px;padding:20px}


