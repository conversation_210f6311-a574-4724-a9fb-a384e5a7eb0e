<?php if (!defined('ABSPATH')) { exit; } ?>
<?php get_header(); ?>

<main id="primary" class="site-main container" style="padding:40px 0">
	<header class="archive-header" style="margin-bottom:32px">
		<h1 class="archive-title">
			<?php
			if (is_category()) {
				single_cat_title();
			} elseif (is_tag()) {
				single_tag_title();
			} elseif (is_author()) {
				the_author();
			} elseif (is_date()) {
				if (is_year()) {
					echo get_the_date('Y');
				} elseif (is_month()) {
					echo get_the_date('F Y');
				} else {
					echo get_the_date();
				}
			} else {
				esc_html_e('Archives', 'volkena');
			}
			?>
		</h1>
		<?php if (is_category() || is_tag()) : ?>
			<div class="archive-description" style="color:var(--c-muted);margin-top:8px">
				<?php echo term_description(); ?>
			</div>
		<?php endif; ?>
	</header>

	<?php if (have_posts()) : ?>
		<div class="posts-grid" style="display:grid;gap:24px">
			<?php while (have_posts()) : the_post(); ?>
				<article id="post-<?php the_ID(); ?>" <?php post_class('post-card card'); ?> style="padding:24px">
					<?php if (has_post_thumbnail()) : ?>
						<div class="post-thumbnail" style="margin-bottom:16px">
							<a href="<?php the_permalink(); ?>">
								<?php the_post_thumbnail('medium', ['style' => 'width:100%;height:200px;object-fit:cover;border-radius:8px']); ?>
							</a>
						</div>
					<?php endif; ?>

					<header class="entry-header" style="margin-bottom:12px">
						<h2 class="entry-title" style="margin:0 0 8px 0">
							<a href="<?php the_permalink(); ?>" style="text-decoration:none;color:inherit">
								<?php the_title(); ?>
							</a>
						</h2>
						<div class="entry-meta" style="color:var(--c-muted);font-size:14px">
							<time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
								<?php echo esc_html(get_the_date()); ?>
							</time>
						</div>
					</header>

					<div class="entry-excerpt">
						<?php the_excerpt(); ?>
					</div>

					<footer class="entry-footer" style="margin-top:16px">
						<a href="<?php the_permalink(); ?>" class="read-more" style="color:var(--c-brand);text-decoration:none;font-weight:500">
							<?php esc_html_e('Read More', 'volkena'); ?> →
						</a>
					</footer>
				</article>
			<?php endwhile; ?>
		</div>

		<div class="pagination" style="margin-top:48px;display:flex;justify-content:center">
			<?php
			the_posts_pagination([
				'prev_text' => '← ' . __('Previous', 'volkena'),
				'next_text' => __('Next', 'volkena') . ' →',
			]);
			?>
		</div>
	<?php else : ?>
		<div class="no-posts" style="text-align:center;padding:48px 0">
			<h2><?php esc_html_e('Nothing found', 'volkena'); ?></h2>
			<p><?php esc_html_e('It seems we can\'t find what you\'re looking for. Perhaps searching can help.', 'volkena'); ?></p>
			<?php get_search_form(); ?>
		</div>
	<?php endif; ?>
</main>

<?php get_footer(); ?>
