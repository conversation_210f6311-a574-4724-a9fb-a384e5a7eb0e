<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> Hearing Theme Functions
 * @package volkena
 */

if (!defined('ABSPATH')) {
	exit;
}

// Setup theme supports, menus, thumbnails
function volkena_setup() {
	// Make theme available for translation
	load_theme_textdomain('volkena', get_template_directory() . '/languages');

	add_theme_support('title-tag');
	add_theme_support('post-thumbnails');
	add_theme_support('align-wide');
	add_theme_support('responsive-embeds');
	add_theme_support('woocommerce');

	register_nav_menus([
		'primary' => __('Primary Menu', 'volkena'),
		'footer' => __('Footer Menu', 'volkena'),
	]);

	// Image sizes tailored for clean product visuals
	add_image_size('volkena-hero', 1920, 900, true);
	add_image_size('volkena-product', 1200, 1200, true);
}
add_action('after_setup_theme', 'volkena_setup');

// Enqueue styles and scripts
function volkena_enqueue_assets() {
	$theme_version = wp_get_theme()->get('Version');

	// Google Fonts: modern, readable sans-serif
	wp_enqueue_style(
		'volkena-fonts',
		'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap',
		[],
		null
	);

	wp_enqueue_style(
		'volkena-styles',
		get_template_directory_uri() . '/assets/css/main.css',
		['volkena-fonts'],
		$theme_version
	);

	wp_enqueue_script(
		'volkena-scripts',
		get_template_directory_uri() . '/assets/js/main.js',
		[],
		$theme_version,
		true
	);
}
add_action('wp_enqueue_scripts', 'volkena_enqueue_assets');

// Register widget areas (optional minimal footer widgets)
function volkena_widgets_init() {
	register_sidebar([
		'name' => __('Footer Widgets', 'volkena'),
		'id' => 'footer-1',
		'before_widget' => '<section class="widget">',
		'after_widget' => '</section>',
		'before_title' => '<h3 class="widget-title">',
		'after_title' => '</h3>',
	]);
}
add_action('widgets_init', 'volkena_widgets_init');

// Allow larger uploads conceptually for Excel import (up to ~300MB)
function volkena_increase_upload_limits($size) {
	return 300 * 1024 * 1024; // 300MB
}
add_filter('upload_size_limit', 'volkena_increase_upload_limits');

// Also bump PHP limits where possible via ini_set (best effort; server may override)
@ini_set('upload_max_filesize', '300M');
@ini_set('post_max_size', '320M');
@ini_set('max_execution_time', '300');
@ini_set('max_input_time', '300');

// Admin: Excel import page (concept)
function volkena_register_admin_menu() {
	add_menu_page(
		__('Völkena Tools', 'volkena'),
		__('Völkena Tools', 'volkena'),
		'manage_options',
		'volkena-tools',
		'volkena_tools_page_render',
		'dashicons-database-import',
		59
	);
}
add_action('admin_menu', 'volkena_register_admin_menu');

function volkena_tools_page_render() {
	if (!current_user_can('manage_options')) {
		return;
	}

	$notice = '';
	if (!empty($_POST['volkena_excel_import_nonce']) && wp_verify_nonce($_POST['volkena_excel_import_nonce'], 'volkena_excel_import')) {
		if (!empty($_FILES['volkena_excel_file']['name'])) {
			$uploaded = wp_handle_upload($_FILES['volkena_excel_file'], ['test_form' => false]);
			if (empty($uploaded['error'])) {
				$notice = __('File uploaded successfully. Parsing is stubbed for concept demo.', 'volkena');
				// TODO: Parse Excel in chunks/stream for memory safety. Concept only.
			} else {
				$notice = sprintf(__('Upload error: %s', 'volkena'), esc_html($uploaded['error']));
			}
		} else {
			$notice = __('Please choose an Excel file to upload.', 'volkena');
		}
	}

	echo '<div class="wrap">';
	echo '<h1>' . esc_html__('Völkena Tools', 'volkena') . '</h1>';
	if ($notice) {
		echo '<div class="notice notice-info"><p>' . esc_html($notice) . '</p></div>';
	}
	echo '<div class="card" style="max-width:900px;background:#fff;border:1px solid #e5e7eb;border-radius:12px;padding:20px">';
	echo '<h2 style="margin-top:0">' . esc_html__('Bulk Product Excel Import (concept)', 'volkena') . '</h2>';
	echo '<p>' . esc_html__('Upload up to 300MB .xlsx/.csv for bulk product management. This demo simulates chunked parsing and progress.', 'volkena') . '</p>';
	echo '<form id="volkena-import-form" method="post" enctype="multipart/form-data" style="display:flex;gap:12px;align-items:center;flex-wrap:wrap">';
	wp_nonce_field('volkena_excel_import', 'volkena_excel_import_nonce');
	echo '<input type="file" name="volkena_excel_file" accept=".xlsx,.xls,.csv" style="padding:8px;border:1px solid #e5e7eb;border-radius:8px" /> ';
	echo '<button class="button button-primary" type="submit">' . esc_html__('Upload', 'volkena') . '</button>';
	echo '</form>';
	echo '<div id="volkena-progress" style="margin-top:16px;display:none">';
	echo '<div style="height:10px;background:#f1f5f9;border-radius:999px;overflow:hidden"><div id="volkena-progress-bar" style="height:10px;width:0;background:#0ea5a3"></div></div>';
	echo '<p id="volkena-progress-text" style="color:#475569;margin:.5em 0 0">0%</p>';
	echo '</div>';
	echo '<script>(function(){var f=document.getElementById("volkena-import-form");var wrap=document.getElementById("volkena-progress");var bar=document.getElementById("volkena-progress-bar");var txt=document.getElementById("volkena-progress-text");if(!f)return;f.addEventListener("submit",function(){wrap.style.display="block";var p=0;var iv=setInterval(function(){p=Math.min(100,p+Math.random()*12);bar.style.width=p+"%";txt.textContent=Math.floor(p)+"%";if(p>=100)clearInterval(iv);},500);});})();</script>';
	echo '</div>';
	echo '</div>';
}

// WooCommerce: basic support hooks for clean wrappers
remove_action('woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10);
remove_action('woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10);

function volkena_woocommerce_wrapper_start() {
	echo '<main id="primary" class="site-main container">';
}
add_action('woocommerce_before_main_content', 'volkena_woocommerce_wrapper_start', 10);

function volkena_woocommerce_wrapper_end() {
	echo '</main>';
}
add_action('woocommerce_after_main_content', 'volkena_woocommerce_wrapper_end', 10);

// Utility: register a simple custom logo support
add_theme_support('custom-logo', [
	'height' => 80,
	'width' => 240,
	'flex-height' => true,
	'flex-width' => true,
]);

?>


