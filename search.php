<?php if (!defined('ABSPATH')) { exit; } ?>
<?php get_header(); ?>

<main id="primary" class="site-main container" style="padding:40px 0">
	<header class="search-header" style="margin-bottom:32px">
		<h1 class="search-title">
			<?php
			printf(
				esc_html__('Search Results for: %s', 'volkena'),
				'<span style="color:var(--c-brand)">' . get_search_query() . '</span>'
			);
			?>
		</h1>
		<div class="search-form-wrapper" style="margin-top:16px">
			<?php get_search_form(); ?>
		</div>
	</header>

	<?php if (have_posts()) : ?>
		<div class="search-results" style="display:grid;gap:24px">
			<?php while (have_posts()) : the_post(); ?>
				<article id="post-<?php the_ID(); ?>" <?php post_class('search-result card'); ?> style="padding:24px">
					<header class="entry-header" style="margin-bottom:12px">
						<h2 class="entry-title" style="margin:0 0 8px 0">
							<a href="<?php the_permalink(); ?>" style="text-decoration:none;color:inherit">
								<?php the_title(); ?>
							</a>
						</h2>
						<div class="entry-meta" style="color:var(--c-muted);font-size:14px;display:flex;gap:16px;flex-wrap:wrap">
							<time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
								<?php echo esc_html(get_the_date()); ?>
							</time>
							<span><?php echo esc_html(get_post_type_object(get_post_type())->labels->singular_name); ?></span>
						</div>
					</header>

					<div class="entry-excerpt">
						<?php the_excerpt(); ?>
					</div>

					<footer class="entry-footer" style="margin-top:16px">
						<a href="<?php the_permalink(); ?>" class="read-more" style="color:var(--c-brand);text-decoration:none;font-weight:500">
							<?php esc_html_e('Read More', 'volkena'); ?> →
						</a>
					</footer>
				</article>
			<?php endwhile; ?>
		</div>

		<div class="pagination" style="margin-top:48px;display:flex;justify-content:center">
			<?php
			the_posts_pagination([
				'prev_text' => '← ' . __('Previous', 'volkena'),
				'next_text' => __('Next', 'volkena') . ' →',
			]);
			?>
		</div>
	<?php else : ?>
		<div class="no-results" style="text-align:center;padding:48px 0">
			<h2><?php esc_html_e('Nothing found', 'volkena'); ?></h2>
			<p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'volkena'); ?></p>
			<div style="margin-top:24px">
				<?php get_search_form(); ?>
			</div>
		</div>
	<?php endif; ?>
</main>

<?php get_footer(); ?>
