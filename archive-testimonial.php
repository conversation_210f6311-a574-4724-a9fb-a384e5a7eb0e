<?php if (!defined('ABSPATH')) { exit; } ?>
<?php get_header(); ?>

<main id="primary" class="site-main container" style="padding:40px 0">
	<header class="archive-header" style="text-align:center;margin-bottom:48px">
		<h1 class="archive-title"><?php esc_html_e('Customer Testimonials', 'volkena'); ?></h1>
		<p class="archive-description" style="color:var(--c-muted);font-size:18px;margin-top:16px">
			<?php esc_html_e('Real stories from our satisfied customers', 'volkena'); ?>
		</p>
	</header>

	<?php if (have_posts()) : ?>
		<div class="testimonials-grid" style="display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:32px">
			<?php while (have_posts()) : the_post(); ?>
				<?php
				$rating = get_post_meta(get_the_ID(), '_testimonial_rating', true);
				$author_name = get_post_meta(get_the_ID(), '_testimonial_author', true);
				$author_location = get_post_meta(get_the_ID(), '_testimonial_location', true);
				?>
				<article id="testimonial-<?php the_ID(); ?>" <?php post_class('testimonial-card card'); ?> style="padding:32px;text-align:center">
					<?php if ($rating) : ?>
						<div class="testimonial-rating" style="margin-bottom:16px">
							<?php for ($i = 1; $i <= 5; $i++) : ?>
								<span style="color:<?php echo $i <= $rating ? 'var(--c-accent)' : 'var(--c-border)'; ?>;font-size:20px">★</span>
							<?php endfor; ?>
						</div>
					<?php endif; ?>

					<blockquote class="testimonial-quote" style="font-size:16px;line-height:1.5;margin:0 0 24px 0;font-style:italic">
						<a href="<?php the_permalink(); ?>" style="text-decoration:none;color:inherit">
							"<?php echo wp_trim_words(get_the_content(), 25, '...'); ?>"
						</a>
					</blockquote>

					<footer class="testimonial-author" style="color:var(--c-muted)">
						<?php if ($author_name) : ?>
							<cite style="font-weight:600;color:var(--c-text);font-style:normal">
								<?php echo esc_html($author_name); ?>
							</cite>
						<?php endif; ?>
						<?php if ($author_location) : ?>
							<div style="margin-top:4px;font-size:14px">
								<?php echo esc_html($author_location); ?>
							</div>
						<?php endif; ?>
					</footer>
				</article>
			<?php endwhile; ?>
		</div>

		<div class="pagination" style="margin-top:48px;display:flex;justify-content:center">
			<?php
			the_posts_pagination([
				'prev_text' => '← ' . __('Previous', 'volkena'),
				'next_text' => __('Next', 'volkena') . ' →',
			]);
			?>
		</div>
	<?php else : ?>
		<div class="no-testimonials" style="text-align:center;padding:48px 0">
			<h2><?php esc_html_e('No testimonials found', 'volkena'); ?></h2>
			<p><?php esc_html_e('Check back soon for customer stories and reviews.', 'volkena'); ?></p>
		</div>
	<?php endif; ?>
</main>

<?php get_footer(); ?>
