(function(){
	// Mobile menu toggle
	var toggle = document.querySelector('.menu-toggle');
	if (toggle){
		var nav = document.querySelector('.nav-primary');
		toggle.addEventListener('click', function(){
			var expanded = this.getAttribute('aria-expanded') === 'true';
			this.setAttribute('aria-expanded', String(!expanded));
			if (nav){ nav.style.display = expanded ? 'none' : 'flex'; }
		});
	}

	// Smooth scroll for same-page anchors
	document.addEventListener('click', function(e){
		var link = e.target.closest('a[href^="#"]');
		if (!link) return;
		var href = link.getAttribute('href');
		if (href.length > 1){
			var el = document.getElementById(href.slice(1));
			if (el){
				e.preventDefault();
				el.scrollIntoView({behavior:'smooth',block:'start'});
			}
		}
	}, {passive:false});
})();


