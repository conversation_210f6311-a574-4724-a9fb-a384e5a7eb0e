<?php
/**
 * The template for displaying product content within loops
 */

if (!defined('ABSPATH')) {
	exit;
}

global $product;

// Ensure visibility.
if (empty($product) || !$product->is_visible()) {
	return;
}
?>
<li <?php wc_product_class('product-card-wrapper', $product); ?>>
	<article class="product-card card" itemscope itemtype="http://schema.org/Product">
		<div class="product-image-wrapper">
			<a href="<?php the_permalink(); ?>" class="product-image-link" aria-label="<?php echo esc_attr(sprintf(__('View %s', 'volkena'), get_the_title())); ?>">
				<?php
				/**
				 * Hook: woocommerce_before_shop_loop_item_title.
				 *
				 * @hooked woocommerce_show_product_loop_sale_flash - 10
				 * @hooked woocommerce_template_loop_product_thumbnail - 10
				 */
				do_action('woocommerce_before_shop_loop_item_title');
				?>
			</a>

			<!-- Product Badges -->
			<div class="product-badges">
				<?php if ($product->is_on_sale()) : ?>
					<span class="badge badge-error product-sale-badge">
						<?php esc_html_e('Sale', 'volkena'); ?>
					</span>
				<?php endif; ?>

				<?php if ($product->is_featured()) : ?>
					<span class="badge badge-primary product-featured-badge">
						<?php esc_html_e('Featured', 'volkena'); ?>
					</span>
				<?php endif; ?>

				<?php
				// Custom badge for new products (products created in last 30 days)
				$created_date = get_the_date('U');
				$thirty_days_ago = strtotime('-30 days');
				if ($created_date > $thirty_days_ago) :
				?>
					<span class="badge badge-info product-new-badge">
						<?php esc_html_e('New', 'volkena'); ?>
					</span>
				<?php endif; ?>
			</div>

			<!-- Quick Actions -->
			<div class="product-quick-actions">
				<?php if (class_exists('WC_Wishlist')) : ?>
					<button class="quick-action-btn wishlist-btn" aria-label="<?php esc_attr_e('Add to wishlist', 'volkena'); ?>">
						<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z"></path>
						</svg>
					</button>
				<?php endif; ?>
			</div>
		</div>

		<div class="product-content">
			<header class="product-header">
				<h3 class="product-title" itemprop="name">
					<a href="<?php the_permalink(); ?>" class="product-title-link">
						<?php the_title(); ?>
					</a>
				</h3>

				<?php
				/**
				 * Hook: woocommerce_after_shop_loop_item_title.
				 *
				 * @hooked woocommerce_template_loop_rating - 5
				 * @hooked woocommerce_template_loop_price - 10
				 */
				do_action('woocommerce_after_shop_loop_item_title');
				?>
			</header>

			<div class="product-excerpt">
				<?php
				$excerpt = get_the_excerpt();
				if ($excerpt) {
					echo '<p>' . wp_trim_words($excerpt, 15, '...') . '</p>';
				}
				?>
			</div>

			<!-- Product Features (from custom fields or attributes) -->
			<?php
			$key_features = get_post_meta(get_the_ID(), '_key_features', true);
			if ($key_features && is_array($key_features)) :
			?>
				<div class="product-features">
					<ul class="features-list">
						<?php foreach (array_slice($key_features, 0, 3) as $feature) : ?>
							<li><?php echo esc_html($feature); ?></li>
						<?php endforeach; ?>
					</ul>
				</div>
			<?php endif; ?>

			<footer class="product-footer">
				<div class="product-pricing" itemprop="offers" itemscope itemtype="http://schema.org/Offer">
					<?php
					/**
					 * Hook: woocommerce_after_shop_loop_item_title.
					 * We're calling this again to get the price in our custom location
					 */
					woocommerce_template_loop_price();
					?>
					
					<?php if ($product->get_price()) : ?>
						<div class="financing-option">
							<?php
							$monthly_price = round($product->get_price() / 12, 0);
							printf(
								esc_html__('or $%d/month', 'volkena'),
								$monthly_price
							);
							?>
						</div>
					<?php endif; ?>
				</div>

				<div class="product-actions">
					<?php
					/**
					 * Hook: woocommerce_after_shop_loop_item.
					 *
					 * @hooked woocommerce_template_loop_add_to_cart - 10
					 */
					do_action('woocommerce_after_shop_loop_item');
					?>
					
					<a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm product-details-btn">
						<?php esc_html_e('View Details', 'volkena'); ?>
					</a>
				</div>
			</footer>
		</div>

		<!-- Schema.org structured data -->
		<meta itemprop="url" content="<?php the_permalink(); ?>">
		<meta itemprop="image" content="<?php echo esc_url(wp_get_attachment_image_url(get_post_thumbnail_id(), 'medium')); ?>">
		<?php if ($product->get_sku()) : ?>
			<meta itemprop="sku" content="<?php echo esc_attr($product->get_sku()); ?>">
		<?php endif; ?>
		<div itemprop="brand" itemscope itemtype="http://schema.org/Brand">
			<meta itemprop="name" content="Völkena Hearing">
		</div>
	</article>
</li>
