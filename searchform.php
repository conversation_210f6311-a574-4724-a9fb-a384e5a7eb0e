<?php if (!defined('ABSPATH')) { exit; } ?>
<form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>" style="display:flex;gap:8px;max-width:400px">
	<label for="search-field" class="screen-reader-text"><?php esc_html_e('Search for:', 'volkena'); ?></label>
	<input 
		type="search" 
		id="search-field" 
		name="s" 
		value="<?php echo esc_attr(get_search_query()); ?>" 
		placeholder="<?php esc_attr_e('Search...', 'volkena'); ?>"
		style="flex:1;padding:12px 16px;border:1px solid var(--c-border);border-radius:8px;font-size:16px"
		aria-label="<?php esc_attr_e('Search', 'volkena'); ?>"
	/>
	<button 
		type="submit" 
		class="search-submit"
		style="padding:12px 20px;background:var(--c-brand);color:white;border:none;border-radius:8px;cursor:pointer;font-weight:500"
		aria-label="<?php esc_attr_e('Submit search', 'volkena'); ?>"
	>
		<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
			<circle cx="11" cy="11" r="8"></circle>
			<path d="m21 21-4.35-4.35"></path>
		</svg>
		<span class="screen-reader-text"><?php esc_html_e('Search', 'volkena'); ?></span>
	</button>
</form>
