<?php
if (!defined('ABSPATH')) { exit; }
get_header('shop');
?>
<main id="primary" class="site-main container" style="padding:32px 0">
	<header class="woocommerce-products-header" style="margin-bottom:16px">
		<h1 class="woocommerce-products-header__title page-title"><?php woocommerce_page_title(); ?></h1>
		<?php do_action('woocommerce_archive_description'); ?>
	</header>
	<?php if (woocommerce_product_loop()) { ?>
		<?php do_action('woocommerce_before_shop_loop'); ?>
		<?php woocommerce_product_loop_start(); ?>
			<?php if (wc_get_loop_prop('total')) {
				while (have_posts()) { the_post();
					do_action('woocommerce_shop_loop');
					wc_get_template_part('content', 'product');
				}
			} ?>
		<?php woocommerce_product_loop_end(); ?>
		<?php do_action('woocommerce_after_shop_loop'); ?>
	<?php } else { ?>
		<?php do_action('woocommerce_no_products_found'); ?>
	<?php } ?>
</main>
<?php get_footer('shop'); ?>


