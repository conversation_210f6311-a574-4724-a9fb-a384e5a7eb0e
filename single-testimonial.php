<?php if (!defined('ABSPATH')) { exit; } ?>
<?php get_header(); ?>

<main id="primary" class="site-main container" style="padding:40px 0">
	<?php while (have_posts()) : the_post(); ?>
		<article id="testimonial-<?php the_ID(); ?>" <?php post_class('single-testimonial'); ?>>
			<div class="testimonial-content card elevated" style="padding:48px;text-align:center;max-width:800px;margin:0 auto">
				<?php
				$rating = get_post_meta(get_the_ID(), '_testimonial_rating', true);
				$author_name = get_post_meta(get_the_ID(), '_testimonial_author', true);
				$author_location = get_post_meta(get_the_ID(), '_testimonial_location', true);
				?>

				<?php if ($rating) : ?>
					<div class="testimonial-rating" style="margin-bottom:24px">
						<?php for ($i = 1; $i <= 5; $i++) : ?>
							<span style="color:<?php echo $i <= $rating ? 'var(--c-accent)' : 'var(--c-border)'; ?>;font-size:24px">★</span>
						<?php endfor; ?>
					</div>
				<?php endif; ?>

				<blockquote class="testimonial-quote" style="font-size:24px;line-height:1.4;margin:0 0 32px 0;font-style:italic;color:var(--c-text)">
					"<?php the_content(); ?>"
				</blockquote>

				<footer class="testimonial-author" style="color:var(--c-muted)">
					<?php if ($author_name) : ?>
						<cite style="font-weight:600;color:var(--c-text);font-style:normal">
							<?php echo esc_html($author_name); ?>
						</cite>
					<?php endif; ?>
					<?php if ($author_location) : ?>
						<div style="margin-top:4px">
							<?php echo esc_html($author_location); ?>
						</div>
					<?php endif; ?>
				</footer>
			</div>

			<div style="text-align:center;margin-top:32px">
				<a href="<?php echo esc_url(get_post_type_archive_link('testimonial')); ?>" class="btn btn-outline">
					← <?php esc_html_e('All Testimonials', 'volkena'); ?>
				</a>
			</div>
		</article>
	<?php endwhile; ?>
</main>

<?php get_footer(); ?>
