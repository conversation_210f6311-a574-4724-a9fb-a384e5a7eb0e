# Völkena Hearing Theme Style Guide

## 🎨 Brand Identity

### Brand Values
- **Trust:** Medical-grade reliability and professionalism
- **Innovation:** Cutting-edge hearing technology
- **Accessibility:** Inclusive design for all users
- **Warmth:** Approachable and caring customer experience

### Target Audience
- **Primary:** Adults 50+ with hearing challenges
- **Secondary:** Family members and caregivers
- **Tertiary:** Healthcare professionals

## 🌈 Color System

### Primary Colors
```css
/* Brand Primary - Soft Teal */
--c-brand: #0ea5a3
--c-brand-50: #f0fdfc
--c-brand-100: #ccfbf1
--c-brand-200: #99f6e4
--c-brand-300: #5eead4
--c-brand-400: #2dd4bf
--c-brand-500: #14b8a6  /* Base */
--c-brand-600: #0d9488
--c-brand-700: #0f766e
--c-brand-800: #115e59
--c-brand-900: #134e4a

/* Accent - Professional Blue */
--c-accent: #4f9ed6
--c-accent-50: #eff6ff
--c-accent-100: #dbeafe
--c-accent-200: #bfdbfe
--c-accent-300: #93c5fd
--c-accent-400: #60a5fa
--c-accent-500: #3b82f6  /* Base */
--c-accent-600: #2563eb
--c-accent-700: #1d4ed8
--c-accent-800: #1e40af
--c-accent-900: #1e3a8a
```

### Neutral Colors
```css
/* Text Colors */
--c-text: #0f172a        /* Primary text - Dark slate */
--c-text-light: #1e293b  /* Secondary text */
--c-muted: #475569       /* Muted text - Slate 600 */
--c-subtle: #64748b      /* Subtle text - Slate 500 */

/* Background Colors */
--c-surface: #ffffff     /* Card/component backgrounds */
--c-bg: #f7fafc         /* Page background - Cool gray 50 */
--c-bg-alt: #f1f5f9     /* Alternative background */

/* Border Colors */
--c-border: #e2e8f0     /* Default borders */
--c-border-strong: #cbd5e1  /* Emphasized borders */
```

### Semantic Colors
```css
/* Success - Green */
--c-success: #10b981
--c-success-bg: #d1fae5
--c-success-border: #a7f3d0

/* Warning - Amber */
--c-warning: #f59e0b
--c-warning-bg: #fef3c7
--c-warning-border: #fde68a

/* Error - Red */
--c-error: #ef4444
--c-error-bg: #fee2e2
--c-error-border: #fecaca

/* Info - Blue */
--c-info: #3b82f6
--c-info-bg: #dbeafe
--c-info-border: #93c5fd
```

### Accessibility Requirements
- **Minimum contrast ratio:** 4.5:1 for normal text
- **Large text contrast:** 3:1 for 18px+ or 14px+ bold
- **Interactive elements:** 3:1 contrast ratio
- **Focus indicators:** Visible and high contrast

## 📝 Typography

### Font Families
```css
/* Primary Font - Inter */
--font-family-primary: 'Inter', system-ui, -apple-system, sans-serif;

/* Secondary Font - IBM Plex Sans */
--font-family-secondary: 'IBM Plex Sans', system-ui, sans-serif;

/* Monospace Font */
--font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
```

### Font Scale
```css
--font-size-xs: 0.75rem    /* 12px */
--font-size-sm: 0.875rem   /* 14px */
--font-size-base: 1rem     /* 16px - Base size */
--font-size-lg: 1.125rem   /* 18px */
--font-size-xl: 1.25rem    /* 20px */
--font-size-2xl: 1.5rem    /* 24px */
--font-size-3xl: 1.875rem  /* 30px */
--font-size-4xl: 2.25rem   /* 36px */
--font-size-5xl: 3rem      /* 48px */
--font-size-6xl: 3.75rem   /* 60px */
--font-size-7xl: 4.5rem    /* 72px */
```

### Font Weights
```css
--font-weight-light: 300
--font-weight-normal: 400
--font-weight-medium: 500
--font-weight-semibold: 600
--font-weight-bold: 700
```

### Line Heights
```css
--line-height-tight: 1.25    /* Headings */
--line-height-snug: 1.375    /* Subheadings */
--line-height-normal: 1.5    /* Body text */
--line-height-relaxed: 1.625 /* Comfortable reading */
--line-height-loose: 2       /* Spacious text */
```

### Typography Usage Guidelines

#### Headings
- **H1:** Page titles, hero headings (font-size-4xl to 6xl)
- **H2:** Section headings (font-size-3xl)
- **H3:** Subsection headings (font-size-2xl)
- **H4:** Component headings (font-size-xl)
- **H5:** Small headings (font-size-lg)
- **H6:** Labels, overlines (font-size-base, uppercase)

#### Body Text
- **Large:** Important content, introductions (font-size-lg)
- **Base:** Standard body text (font-size-base)
- **Small:** Captions, metadata (font-size-sm)

## 📏 Spacing System

### Spacing Scale
```css
--space-px: 1px
--space-0: 0
--space-xs: 0.25rem   /* 4px */
--space-sm: 0.5rem    /* 8px */
--space-md: 0.75rem   /* 12px */
--space-lg: 1rem      /* 16px */
--space-xl: 1.5rem    /* 24px */
--space-2xl: 2rem     /* 32px */
--space-3xl: 3rem     /* 48px */
--space-4xl: 4rem     /* 64px */
--space-5xl: 6rem     /* 96px */
--space-6xl: 8rem     /* 128px */
```

### Usage Guidelines
- **Component padding:** space-lg to space-xl
- **Section spacing:** space-3xl to space-4xl
- **Element margins:** space-sm to space-lg
- **Grid gaps:** space-lg to space-2xl

## 🔲 Layout System

### Container Widths
```css
--container-sm: 640px
--container-md: 768px
--container-lg: 1024px
--container-xl: 1280px
--container-2xl: 1536px
--container-max: 1200px  /* Theme default */
```

### Breakpoints
```css
/* Mobile First Approach */
@media (min-width: 640px)  { /* sm */ }
@media (min-width: 768px)  { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

### Grid System
- **12-column grid** for complex layouts
- **CSS Grid** for modern layouts
- **Flexbox** for component alignment
- **Gap-based spacing** instead of margins

## 🎯 Component Library

### Buttons
```css
/* Primary Button */
.btn-primary {
  background: var(--c-brand);
  color: var(--c-surface);
  border: 1px solid var(--c-brand);
}

/* Secondary Button */
.btn-outline {
  background: transparent;
  color: var(--c-text);
  border: 1px solid var(--c-border-strong);
}

/* Button Sizes */
.btn-sm { padding: 8px 16px; font-size: 14px; }
.btn-md { padding: 12px 24px; font-size: 16px; }
.btn-lg { padding: 16px 32px; font-size: 18px; }
```

### Cards
```css
.card {
  background: var(--c-surface);
  border: 1px solid var(--c-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-lg);
}
```

### Forms
```css
.form-input {
  border: 1px solid var(--c-border);
  border-radius: var(--radius-md);
  padding: 12px 16px;
  font-size: var(--font-size-base);
}

.form-input:focus {
  border-color: var(--c-brand);
  box-shadow: 0 0 0 3px var(--c-brand-100);
}
```

## 🎨 Visual Elements

### Border Radius
```css
--radius-none: 0
--radius-sm: 0.125rem   /* 2px */
--radius-md: 0.375rem   /* 6px */
--radius-lg: 0.5rem     /* 8px */
--radius-xl: 0.75rem    /* 12px */
--radius-2xl: 1rem      /* 16px */
--radius-full: 9999px   /* Fully rounded */
```

### Shadows
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
```

### Transitions
```css
--transition-fast: 150ms ease-in-out;
--transition-base: 250ms ease-in-out;
--transition-slow: 350ms ease-in-out;
```

## ♿ Accessibility Guidelines

### Color Contrast
- **Text on background:** Minimum 4.5:1 ratio
- **Large text:** Minimum 3:1 ratio
- **Interactive elements:** Minimum 3:1 ratio

### Focus States
- **Visible focus indicators** for all interactive elements
- **High contrast** focus rings
- **Consistent focus styling** across components

### Typography
- **Minimum 16px** base font size
- **Maximum 75 characters** per line
- **1.5x line height** for body text
- **Clear hierarchy** with proper heading structure

### Interactive Elements
- **Minimum 44px** touch target size
- **Clear hover states** for all interactive elements
- **Descriptive link text** (avoid "click here")
- **Form labels** associated with inputs

## 📱 Responsive Design

### Mobile-First Approach
1. **Design for mobile** (320px+)
2. **Enhance for tablet** (768px+)
3. **Optimize for desktop** (1024px+)

### Key Considerations
- **Touch-friendly** interface elements
- **Readable text** without zooming
- **Accessible navigation** on small screens
- **Fast loading** on mobile networks

## 🎯 Brand Voice & Tone

### Voice Characteristics
- **Professional** yet approachable
- **Empathetic** and understanding
- **Clear** and jargon-free
- **Confident** in expertise

### Tone Guidelines
- **Warm** and caring
- **Informative** without being overwhelming
- **Encouraging** and supportive
- **Respectful** of user concerns

### Content Principles
- **Clarity first** - avoid medical jargon
- **Benefit-focused** - emphasize outcomes
- **Inclusive language** - consider all users
- **Action-oriented** - guide users to next steps

---

This style guide ensures consistent, accessible, and professional design across all Völkena Hearing brand touchpoints.
