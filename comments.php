<?php
/**
 * Comments template
 * Provides a minimal, accessible comments layout to satisfy WP requirements.
 * @package volkena
 */

if (!defined('ABSPATH')) { exit; }

// Password protected post
if (post_password_required()) {
	return;
}
?>

<div id="comments" class="comments-area container" style="padding:20px 0">
	<?php if (have_comments()) : ?>
		<h2 class="comments-title">
			<?php
				$comments_number = get_comments_number();
				if ('1' === $comments_number) {
					echo esc_html__('One comment', 'volkena');
				} else {
					echo esc_html(sprintf(_n('%s comment', '%s comments', $comments_number, 'volkena'), number_format_i18n($comments_number)));
				}
			?>
		</h2>

		<ol class="comment-list" style="list-style:none;padding:0;display:grid;gap:12px">
			<?php
				wp_list_comments([
					'style' => 'ol',
					'short_ping' => true,
					'avatar_size' => 40,
				]);
			?>
		</ol>

		<?php the_comments_navigation(); ?>
	<?php endif; ?>

	<?php if (!comments_open() && get_comments_number()) : ?>
		<p class="no-comments" style="color:var(--c-muted)"><?php esc_html_e('Comments are closed.', 'volkena'); ?></p>
	<?php endif; ?>

	<?php comment_form(); ?>
</div>


