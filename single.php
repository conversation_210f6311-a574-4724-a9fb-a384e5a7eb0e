<?php if (!defined('ABSPATH')) { exit; } ?>
<?php get_header(); ?>

<main id="primary" class="site-main container" style="padding:40px 0">
	<?php while (have_posts()) : the_post(); ?>
		<article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?>>
			<header class="entry-header" style="margin-bottom:32px">
				<h1 class="entry-title" style="margin-bottom:16px"><?php the_title(); ?></h1>
				<div class="entry-meta" style="color:var(--c-muted);font-size:14px;display:flex;gap:16px;flex-wrap:wrap">
					<time datetime="<?php echo esc_attr(get_the_date('c')); ?>" style="display:flex;align-items:center;gap:4px">
						<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<circle cx="12" cy="12" r="10"></circle>
							<polyline points="12,6 12,12 16,14"></polyline>
						</svg>
						<?php echo esc_html(get_the_date()); ?>
					</time>
					<?php if (has_category()) : ?>
						<div style="display:flex;align-items:center;gap:4px">
							<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
								<path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
								<line x1="7" y1="7" x2="7.01" y2="7"></line>
							</svg>
							<?php the_category(', '); ?>
						</div>
					<?php endif; ?>
				</div>
			</header>

			<?php if (has_post_thumbnail()) : ?>
				<div class="entry-featured-image" style="margin-bottom:32px">
					<?php the_post_thumbnail('large', ['style' => 'width:100%;height:auto;border-radius:12px']); ?>
				</div>
			<?php endif; ?>

			<div class="entry-content prose">
				<?php the_content(); ?>
			</div>

			<?php if (has_tag()) : ?>
				<footer class="entry-footer" style="margin-top:32px;padding-top:24px;border-top:1px solid var(--c-border)">
					<div class="entry-tags" style="display:flex;align-items:center;gap:8px;flex-wrap:wrap">
						<span style="color:var(--c-muted);font-weight:500"><?php esc_html_e('Tags:', 'volkena'); ?></span>
						<?php the_tags('', ' ', ''); ?>
					</div>
				</footer>
			<?php endif; ?>
		</article>

		<?php
		// Navigation between posts
		$prev_post = get_previous_post();
		$next_post = get_next_post();
		if ($prev_post || $next_post) :
		?>
			<nav class="post-navigation" style="margin-top:48px;display:grid;grid-template-columns:1fr 1fr;gap:16px">
				<?php if ($prev_post) : ?>
					<a href="<?php echo esc_url(get_permalink($prev_post)); ?>" class="nav-previous card" style="padding:20px;text-decoration:none;color:inherit">
						<div style="color:var(--c-muted);font-size:14px;margin-bottom:4px">← <?php esc_html_e('Previous', 'volkena'); ?></div>
						<div style="font-weight:500"><?php echo esc_html(get_the_title($prev_post)); ?></div>
					</a>
				<?php else : ?>
					<div></div>
				<?php endif; ?>

				<?php if ($next_post) : ?>
					<a href="<?php echo esc_url(get_permalink($next_post)); ?>" class="nav-next card" style="padding:20px;text-decoration:none;color:inherit;text-align:right">
						<div style="color:var(--c-muted);font-size:14px;margin-bottom:4px"><?php esc_html_e('Next', 'volkena'); ?> →</div>
						<div style="font-weight:500"><?php echo esc_html(get_the_title($next_post)); ?></div>
					</a>
				<?php endif; ?>
			</nav>
		<?php endif; ?>

		<?php
		// If comments are open or we have at least one comment, load up the comment template.
		if (comments_open() || get_comments_number()) :
			comments_template();
		endif;
		?>

	<?php endwhile; ?>
</main>

<?php get_footer(); ?>
